using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using FluentAssertions;
using Moq;
using SmaTrendFollower.Services;
using SmaTrendFollower.Models;
using SmaTrendFollower.Configuration;
using StackExchange.Redis;

namespace SmaTrendFollower.Tests.Integration;

/// <summary>
/// Integration tests for Phase 7 services
/// Tests the interaction between SlippageEstimator, TickBarBuilder, and SmartTradeThrottler
/// </summary>
public class Phase7ServicesIntegrationTests : IDisposable
{
    private readonly ServiceProvider _serviceProvider;
    private readonly ISlippageEstimator _slippageEstimator;
    private readonly ITickBarBuilder _tickBarBuilder;
    private readonly ISmartTradeThrottler _smartTradeThrottler;

    public Phase7ServicesIntegrationTests()
    {
        var services = new ServiceCollection();
        
        // Add logging
        services.AddLogging(builder => builder.AddConsole().SetMinimumLevel(LogLevel.Debug));
        
        // Add mocked dependencies
        var mockRedisService = new Mock<IRedisService>();
        var mockTimeProvider = new Mock<ITimeProvider>();
        var mockTickStreamService = new Mock<ITickStreamService>();
        var mockVolatilityGuard = new Mock<ITickVolatilityGuard>();
        
        // Setup time provider to return consistent time
        var fixedTime = new DateTime(2024, 1, 15, 10, 30, 0, DateTimeKind.Utc);
        mockTimeProvider.Setup(x => x.GetUtcNow()).Returns(fixedTime);
        
        // Setup Redis mock to return empty values
        var mockDatabase = new Mock<IDatabase>();
        mockDatabase.Setup(x => x.StringGetAsync(It.IsAny<RedisKey>(), It.IsAny<CommandFlags>()))
                   .ReturnsAsync(RedisValue.Null);
        mockDatabase.Setup(x => x.ListRangeAsync(It.IsAny<RedisKey>(), It.IsAny<long>(), It.IsAny<long>(), It.IsAny<CommandFlags>()))
                   .ReturnsAsync(Array.Empty<RedisValue>());
        mockRedisService.Setup(x => x.GetDatabaseAsync()).ReturnsAsync(mockDatabase.Object);
        
        services.AddSingleton(mockRedisService.Object);
        services.AddSingleton(mockTimeProvider.Object);
        services.AddSingleton(mockTickStreamService.Object);
        services.AddSingleton(mockVolatilityGuard.Object);
        
        // Add Phase 7 services
        services.AddScoped<ISlippageEstimator, SlippageEstimator>();
        services.AddScoped<ITickBarBuilder, TickBarBuilder>();
        services.AddScoped<ISmartTradeThrottler, SmartTradeThrottler>();
        
        _serviceProvider = services.BuildServiceProvider();
        
        _slippageEstimator = _serviceProvider.GetRequiredService<ISlippageEstimator>();
        _tickBarBuilder = _serviceProvider.GetRequiredService<ITickBarBuilder>();
        _smartTradeThrottler = _serviceProvider.GetRequiredService<ISmartTradeThrottler>();
    }

    [Fact]
    public async Task StartAllPhase7Services_WithSameSymbols_ShouldStartSuccessfully()
    {
        // Arrange
        var symbols = new[] { "AAPL", "MSFT", "GOOGL" };
        var barConfigs = new[]
        {
            new BarBuildingConfig(CustomBarType.Renko, 0.50m),
            new BarBuildingConfig(CustomBarType.Volume, 10000m),
            new BarBuildingConfig(CustomBarType.Time, 5m)
        };

        // Act
        await _slippageEstimator.StartTrackingAsync(symbols);
        await _tickBarBuilder.StartBuildingAsync(symbols, barConfigs);
        await _smartTradeThrottler.StartThrottlingAsync(symbols);

        // Assert
        _slippageEstimator.GetStatus().Should().Be(SlippageTrackingStatus.Active);
        _tickBarBuilder.GetStatus().Should().Be(BarBuildingStatus.Active);
        _smartTradeThrottler.GetStatus().Should().Be(ThrottlingServiceStatus.Active);
        
        _slippageEstimator.GetTrackedSymbols().Should().BeEquivalentTo(symbols);
        _tickBarBuilder.GetProcessedSymbols().Should().BeEquivalentTo(symbols);
        _smartTradeThrottler.GetMonitoredSymbols().Should().BeEquivalentTo(symbols);
    }

    [Fact]
    public async Task SlippageEstimator_RecordOrderSubmissionAndFill_ShouldCalculateSlippage()
    {
        // Arrange
        var symbol = "AAPL";
        await _slippageEstimator.StartTrackingAsync(new[] { symbol });

        var submission = new OrderSubmissionRecord(
            OrderId: "ORDER123",
            Symbol: symbol,
            Side: OrderSide.Buy,
            Quantity: 100m,
            LimitPrice: 150.00m,
            MarketPrice: 149.95m,
            BidPrice: 149.90m,
            AskPrice: 150.05m,
            Spread: 0.15m,
            SubmissionTime: DateTime.UtcNow,
            OrderType: "LIMIT"
        );

        var fill = new OrderFillRecord(
            OrderId: "ORDER123",
            Symbol: symbol,
            Side: OrderSide.Buy,
            FilledQuantity: 100m,
            FillPrice: 150.02m,
            FillTime: DateTime.UtcNow.AddSeconds(5),
            Exchange: "NASDAQ",
            Commission: 1.00m
        );

        // Act
        await _slippageEstimator.RecordOrderSubmissionAsync(submission);
        await _slippageEstimator.RecordOrderFillAsync(fill);

        // Assert
        var estimate = await _slippageEstimator.EstimateSlippageAsync(symbol, 100m, OrderSide.Buy, 0.15m);
        estimate.Should().NotBeNull();
        estimate.Symbol.Should().Be(symbol);
        estimate.ExpectedSlippageBps.Should().BeGreaterThan(0);
    }

    [Fact]
    public async Task TickBarBuilder_ProcessTradeTicks_ShouldBuildRenkoBars()
    {
        // Arrange
        var symbol = "AAPL";
        var config = new BarBuildingConfig(CustomBarType.Renko, 0.50m); // 50 cent bricks
        
        await _tickBarBuilder.StartBuildingAsync(new[] { symbol }, new[] { config });

        var ticks = new[]
        {
            new TradeTick(symbol, 150.00m, 100, DateTime.UtcNow, "NASDAQ", ""),
            new TradeTick(symbol, 150.25m, 200, DateTime.UtcNow.AddSeconds(1), "NASDAQ", ""),
            new TradeTick(symbol, 150.55m, 150, DateTime.UtcNow.AddSeconds(2), "NASDAQ", ""), // Should complete brick
            new TradeTick(symbol, 150.75m, 100, DateTime.UtcNow.AddSeconds(3), "NASDAQ", "")
        };

        // Act
        foreach (var tick in ticks)
        {
            await _tickBarBuilder.ProcessTradeTickAsync(tick);
        }

        // Allow some time for processing
        await Task.Delay(100);

        // Assert
        var currentBar = await _tickBarBuilder.GetCurrentBarAsync(symbol, CustomBarType.Renko);
        currentBar.Should().NotBeNull();
        currentBar!.Symbol.Should().Be(symbol);
        currentBar.BarType.Should().Be(CustomBarType.Renko);
        
        var completedBars = await _tickBarBuilder.GetCompletedBarsAsync(symbol, CustomBarType.Renko, 10);
        completedBars.Should().NotBeEmpty();
    }

    [Fact]
    public async Task SmartTradeThrottler_FrequentTrades_ShouldThrottleAfterLimit()
    {
        // Arrange
        var symbol = "AAPL";
        var config = new TradeThrottlerConfig(MaxTradesPerHour: 2, MinTimeBetweenTrades: TimeSpan.FromMinutes(1));
        
        await _smartTradeThrottler.UpdateConfigurationAsync(config);
        await _smartTradeThrottler.StartThrottlingAsync(new[] { symbol });

        var tradeRequest = new TradeRequest(
            Symbol: symbol,
            Direction: TradeDirection.Buy,
            Quantity: 100m,
            Price: 150.00m,
            CurrentVolatility: 0.02m,
            TickRange: 0.01m,
            SignalStrength: TradeSignalStrength.Strong,
            RequestTime: DateTime.UtcNow
        );

        // Act & Assert
        // First trade should be allowed
        var decision1 = await _smartTradeThrottler.ShouldAllowTradeAsync(symbol, tradeRequest);
        decision1.IsAllowed.Should().BeTrue();

        // Record first trade execution
        var execution1 = new TradeExecution(
            Symbol: symbol,
            Direction: TradeDirection.Buy,
            ExecutedQuantity: 100m,
            ExecutedPrice: 150.00m,
            ExecutionTime: DateTime.UtcNow,
            ExecutionId: "EXEC1",
            Slippage: 0.02m,
            ExecutionLatency: TimeSpan.FromMilliseconds(50)
        );
        await _smartTradeThrottler.RecordTradeExecutionAsync(symbol, execution1);

        // Second trade should be allowed
        var decision2 = await _smartTradeThrottler.ShouldAllowTradeAsync(symbol, tradeRequest);
        decision2.IsAllowed.Should().BeTrue();

        // Record second trade execution
        var execution2 = execution1 with { ExecutionId = "EXEC2", ExecutionTime = DateTime.UtcNow.AddSeconds(30) };
        await _smartTradeThrottler.RecordTradeExecutionAsync(symbol, execution2);

        // Third trade should be throttled (exceeds hourly limit)
        var decision3 = await _smartTradeThrottler.ShouldAllowTradeAsync(symbol, tradeRequest);
        decision3.IsAllowed.Should().BeFalse();
        decision3.Reason.Should().Be(ThrottleReason.FrequencyLimit);
    }

    [Fact]
    public async Task IntegratedWorkflow_SlippageEstimationWithThrottling_ShouldWorkTogether()
    {
        // Arrange
        var symbol = "AAPL";
        
        await _slippageEstimator.StartTrackingAsync(new[] { symbol });
        await _smartTradeThrottler.StartThrottlingAsync(new[] { symbol });

        // Act - Simulate a complete trading workflow
        
        // 1. Check if trade should be allowed
        var tradeRequest = new TradeRequest(
            Symbol: symbol,
            Direction: TradeDirection.Buy,
            Quantity: 100m,
            Price: 150.00m,
            CurrentVolatility: 0.02m,
            TickRange: 0.01m,
            SignalStrength: TradeSignalStrength.Strong,
            RequestTime: DateTime.UtcNow
        );

        var throttleDecision = await _smartTradeThrottler.ShouldAllowTradeAsync(symbol, tradeRequest);
        
        // 2. If allowed, get slippage estimate
        SlippageEstimate? slippageEstimate = null;
        if (throttleDecision.IsAllowed)
        {
            slippageEstimate = await _slippageEstimator.EstimateSlippageAsync(
                symbol, tradeRequest.Quantity, OrderSide.Buy, 0.15m);
        }

        // 3. Record order submission
        if (throttleDecision.IsAllowed)
        {
            var submission = new OrderSubmissionRecord(
                OrderId: "ORDER123",
                Symbol: symbol,
                Side: OrderSide.Buy,
                Quantity: tradeRequest.Quantity,
                LimitPrice: tradeRequest.Price,
                MarketPrice: tradeRequest.Price - 0.05m,
                BidPrice: tradeRequest.Price - 0.10m,
                AskPrice: tradeRequest.Price + 0.05m,
                Spread: 0.15m,
                SubmissionTime: DateTime.UtcNow,
                OrderType: "LIMIT"
            );

            await _slippageEstimator.RecordOrderSubmissionAsync(submission);
        }

        // 4. Record execution and update throttling
        if (throttleDecision.IsAllowed)
        {
            var fill = new OrderFillRecord(
                OrderId: "ORDER123",
                Symbol: symbol,
                Side: OrderSide.Buy,
                FilledQuantity: tradeRequest.Quantity,
                FillPrice: tradeRequest.Price + 0.02m, // Small slippage
                FillTime: DateTime.UtcNow.AddSeconds(2),
                Exchange: "NASDAQ",
                Commission: 1.00m
            );

            await _slippageEstimator.RecordOrderFillAsync(fill);

            var execution = new TradeExecution(
                Symbol: symbol,
                Direction: tradeRequest.Direction,
                ExecutedQuantity: fill.FilledQuantity,
                ExecutedPrice: fill.FillPrice,
                ExecutionTime: fill.FillTime,
                ExecutionId: "EXEC123",
                Slippage: 0.02m,
                ExecutionLatency: TimeSpan.FromSeconds(2)
            );

            await _smartTradeThrottler.RecordTradeExecutionAsync(symbol, execution);
        }

        // Assert
        throttleDecision.IsAllowed.Should().BeTrue();
        slippageEstimate.Should().NotBeNull();
        slippageEstimate!.Symbol.Should().Be(symbol);
        
        var throttlingStatus = await _smartTradeThrottler.GetThrottlingStatusAsync(symbol);
        throttlingStatus.Should().NotBeNull();
        throttlingStatus!.TradesInLastHour.Should().Be(1);
        
        var slippageStats = await _slippageEstimator.GetSlippageStatisticsAsync(symbol);
        // Stats might be null initially, but the service should be tracking
    }

    [Fact]
    public async Task TickBarBuilder_VolumeAndTimeBars_ShouldBuildConcurrently()
    {
        // Arrange
        var symbol = "AAPL";
        var configs = new[]
        {
            new BarBuildingConfig(CustomBarType.Volume, 1000m), // 1000 share volume bars
            new BarBuildingConfig(CustomBarType.Time, 1m)       // 1 minute time bars
        };
        
        await _tickBarBuilder.StartBuildingAsync(new[] { symbol }, configs);

        var ticks = new[]
        {
            new TradeTick(symbol, 150.00m, 300, DateTime.UtcNow, "NASDAQ", ""),
            new TradeTick(symbol, 150.05m, 400, DateTime.UtcNow.AddSeconds(10), "NASDAQ", ""),
            new TradeTick(symbol, 150.10m, 350, DateTime.UtcNow.AddSeconds(20), "NASDAQ", ""), // Total: 1050 shares
            new TradeTick(symbol, 150.15m, 200, DateTime.UtcNow.AddSeconds(30), "NASDAQ", "")
        };

        // Act
        foreach (var tick in ticks)
        {
            await _tickBarBuilder.ProcessTradeTickAsync(tick);
        }

        await Task.Delay(100); // Allow processing

        // Assert
        var volumeBar = await _tickBarBuilder.GetCurrentBarAsync(symbol, CustomBarType.Volume);
        var timeBar = await _tickBarBuilder.GetCurrentBarAsync(symbol, CustomBarType.Time);

        volumeBar.Should().NotBeNull();
        timeBar.Should().NotBeNull();

        // Volume bar should have been completed and a new one started
        var completedVolumeBars = await _tickBarBuilder.GetCompletedBarsAsync(symbol, CustomBarType.Volume, 5);
        completedVolumeBars.Should().NotBeEmpty();

        // Time bar should still be building (unless 1 minute has passed)
        timeBar!.IsComplete.Should().BeFalse();
        timeBar.Volume.Should().BeGreaterThan(0);
    }

    [Fact]
    public async Task ConfigurationUpdates_ShouldApplyToAllServices()
    {
        // Arrange
        var symbols = new[] { "AAPL" };
        
        await _slippageEstimator.StartTrackingAsync(symbols);
        await _smartTradeThrottler.StartThrottlingAsync(symbols);

        // Act - Update configurations
        var slippageConfig = new SlippageEstimatorConfig(
            HistoryDays: 60,
            MinSamplesForModel: 20,
            HighSlippageThreshold: 0.10m
        );

        var throttlerConfig = new TradeThrottlerConfig(
            MaxTradesPerHour: 5,
            MaxTradesPerDay: 25,
            MinTimeBetweenTrades: TimeSpan.FromMinutes(10)
        );

        var symbolRules = new SymbolThrottlingRules(
            Symbol: "AAPL",
            MaxTradesPerHour: 3,
            MinTimeBetweenTrades: TimeSpan.FromMinutes(15)
        );

        await _slippageEstimator.UpdateConfigurationAsync(slippageConfig);
        await _smartTradeThrottler.UpdateConfigurationAsync(throttlerConfig);
        await _smartTradeThrottler.UpdateSymbolRulesAsync("AAPL", symbolRules);

        // Assert
        // Verify that services are still active after configuration updates
        _slippageEstimator.GetStatus().Should().Be(SlippageTrackingStatus.Active);
        _smartTradeThrottler.GetStatus().Should().Be(ThrottlingServiceStatus.Active);

        // Test that new configuration is applied
        var tradeRequest = new TradeRequest(
            Symbol: "AAPL",
            Direction: TradeDirection.Buy,
            Quantity: 100m,
            Price: 150.00m,
            CurrentVolatility: 0.02m,
            TickRange: 0.01m,
            SignalStrength: TradeSignalStrength.Strong,
            RequestTime: DateTime.UtcNow
        );

        var decision = await _smartTradeThrottler.ShouldAllowTradeAsync("AAPL", tradeRequest);
        decision.Should().NotBeNull();
    }

    [Fact]
    public async Task StopAllPhase7Services_ShouldStopCleanly()
    {
        // Arrange
        var symbols = new[] { "AAPL", "MSFT" };
        var barConfigs = new[] { new BarBuildingConfig(CustomBarType.Renko, 0.25m) };
        
        await _slippageEstimator.StartTrackingAsync(symbols);
        await _tickBarBuilder.StartBuildingAsync(symbols, barConfigs);
        await _smartTradeThrottler.StartThrottlingAsync(symbols);

        // Verify services are active
        _slippageEstimator.GetStatus().Should().Be(SlippageTrackingStatus.Active);
        _tickBarBuilder.GetStatus().Should().Be(BarBuildingStatus.Active);
        _smartTradeThrottler.GetStatus().Should().Be(ThrottlingServiceStatus.Active);

        // Act
        await _slippageEstimator.StopTrackingAsync();
        await _tickBarBuilder.StopBuildingAsync();
        await _smartTradeThrottler.StopThrottlingAsync();

        // Assert
        _slippageEstimator.GetStatus().Should().Be(SlippageTrackingStatus.Stopped);
        _tickBarBuilder.GetStatus().Should().Be(BarBuildingStatus.Stopped);
        _smartTradeThrottler.GetStatus().Should().Be(ThrottlingServiceStatus.Stopped);
        
        _slippageEstimator.GetTrackedSymbols().Should().BeEmpty();
        _tickBarBuilder.GetProcessedSymbols().Should().BeEmpty();
        _smartTradeThrottler.GetMonitoredSymbols().Should().BeEmpty();
    }

    public void Dispose()
    {
        _slippageEstimator?.Dispose();
        _tickBarBuilder?.Dispose();
        _smartTradeThrottler?.Dispose();
        _serviceProvider?.Dispose();
    }
}
